import React, { useState, useEffect } from "react";
import "./App.css";
import Menu from "./components/Menu";
import MainForm from "./pages/MainForm";
import FormBPage from "./pages/FormBPage";
import FormRepairDossier from "./pages/FormRepairDossier";
import UpdateProcessingContent from "./pages/UpdateProcessingContent";
import LoginForm from "./pages/LoginForm";
import logo from "./images/logo.png";
import { FaSignOutAlt } from "react-icons/fa"; // Import các biểu tượng từ React Icons
import EditDossier from "./pages/EditDossier";
import FormDVCLTPage from "./pages/FormDVCLT";
import LoadingBar from './components/LoadingBar';
import FormDVCQGPage from "./pages/FormDVCQGPage";
import FormVNeIDPage from "./pages/FormVNeIDPage";
import DossierManagement from "./pages/DossierManagement";
import RancherDashboard from "./pages/Rancher";

const App: React.FC = () => {
  const [view, setView] = useState<string>("login");
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);
  const [username, setUsername] = useState<string | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [expiresIn, setExpiresIn] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const storedToken = localStorage.getItem("access_token");
    const storedExpiresAt = localStorage.getItem("expires_at");
    const storedUserName = localStorage.getItem("username-showUI");
  
    if (storedToken && storedExpiresAt) {
      const expirationDate = new Date(storedExpiresAt);
  
      // Check if the current time is before the expiration date
      if (Date.now() < expirationDate.getTime()) {
        setAccessToken(storedToken);
        setIsLoggedIn(true);
        setView("formVBDLIS"); // Redirect to default view after login
        setUsername(storedUserName || ""); // Use an empty string if storedUserName is null
      } else {
        setView("login"); // Token expired, show login form
        setAccessToken(null);
        setIsLoggedIn(false);
      }
    } else {
      setView("login"); // No token found, show login form
      setAccessToken(null);
      setIsLoggedIn(false);
    }
  }, []);
  
  

  const handleViewChange = (view: string) => {
    setView(view);
  };

  const storeToken = (token: string, expiresIn: number) => {
    if (isNaN(expiresIn) || expiresIn <= 0) {
      console.error("Invalid expiresIn value:", expiresIn);
      return;
    }
  
    const expirationDate = new Date(Date.now() + expiresIn * 1000);
    localStorage.setItem("access_token", token);
    localStorage.setItem("expires_at", expirationDate.toISOString());
  };

  const handleLoginSuccess = (username: string, tokenData: any) => {
    if (!tokenData || !tokenData.access_token || !tokenData.expires_in) {
      console.error("Invalid token data received:", tokenData);
      return;
    }
  
    try {
      setUsername(username);
      setAccessToken(tokenData.access_token);
      setExpiresIn(tokenData.expires_in); // Set expiresIn state
      setIsLoggedIn(true);
      setView("formVBDLIS"); // Redirect to default view after login
  
      // Store token and expires_in in localStorage
      localStorage.setItem("access_token", tokenData.access_token);
      localStorage.setItem("expires_in", tokenData.expires_in.toString());
  
      const expiresInNumber = Number(tokenData.expires_in);
      if (isNaN(expiresInNumber) || expiresInNumber <= 0) {
        console.error("Invalid expires_in value:", tokenData.expires_in);
        return;
      }
  
      const expirationDate = new Date(Date.now() + expiresInNumber * 1000);
      localStorage.setItem("expires_at", expirationDate.toISOString());
    } catch (error) {
      console.error("Failed to handle login success:", error);
    }
  };
  
  

  const handleLogout = () => {
    setUsername("");
    setAccessToken(null);
    setExpiresIn(null);
    setIsLoggedIn(false);
    setView("login");
    localStorage.removeItem("access_token");
    localStorage.removeItem("expires_in");
    localStorage.removeItem("expires_at");
  };

  const renderView = () => {
    switch (view) {
      case "formVBDLIS":
        return <MainForm />;
      case "formBTXH":
        return <FormBPage />;
      case "formDVCLT":
        return <FormDVCLTPage />;
      case "formDVCQG":
          return <FormDVCQGPage />;
      case "formVNeID":
        return <FormVNeIDPage />;
      case "formRepairDossier":
        return <FormRepairDossier />;
      case "updateProcessingContent":
        return <UpdateProcessingContent />;
      case "login":
        return <LoginForm onLoginSuccess={handleLoginSuccess} />;
      case "editDossier":
        return <EditDossier />;
      case "dossierManagement":
        return <DossierManagement />;
      case "rancherDashboard":
        return <RancherDashboard />;
      default:
        return <MainForm />;
    }
  };

  return (
    <div className="app-container">
      <LoadingBar isLoading={isLoading} />
      <header className="header">
       <div className="header-logo"></div>
        
        <div className="header-content">
          <img src={logo} alt="Logo" className="header-logo" />
          <div className="header-title-group">
            <h1 className="header-title">Tool hỗ trợ iGate2.0</h1>
            <p className="header-subtitle">Chăm sóc khách hàng là niềm vui của chúng tôi</p>
          </div>
        </div>
        {isLoggedIn && (
          <div className="user-info">
            <span>{username}</span>
            <button onClick={handleLogout}>
              <FaSignOutAlt />
            </button>
          </div>
        )}
         {!isLoggedIn && (
        <div></div>)}
      </header>
      {isLoggedIn && (
        <div className="main-content">
          <Menu setView={handleViewChange} />
          <div className="content">{renderView()}</div>
        </div>
      )}
      {!isLoggedIn && (
        <div className="login-container">
          <LoginForm onLoginSuccess={handleLoginSuccess} />
        </div>
      )}
      <footer className="footer">
        <p>© VNPT Cà Mau</p>
      </footer>
    </div>
  );
};

export default App;
