import React, { useState } from "react";
import TextArea from "../components/TextArea";
import SelectOption from "../components/SelectOptionDVCLT";
import MessageDisplay from "../components/MessageDisplay";
import { FaTrash, FaSync, <PERSON>a<PERSON><PERSON>board<PERSON>ist, FaReply } from "react-icons/fa";
import "./css/MainForm.css";
// import các biến cấu hình
import { API_BASE_URL, CONFIG_VBDLIS_ID } from "../config";
import Snackbar from "../components/Snackbar"; // Import Snackbar
import axios from "axios";
import qs from "qs";
import { set } from "react-datepicker/dist/date_utils";

const FormDVCLTPage: React.FC = () => {
  const [inputText, setInputText] = useState<string>("");
  const [option, setOption] = useState<string>("Tiếp nhận qua PM Hộ tịch tư pháp");
  const [message, setMessage] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [logResults, setLogResults] = useState<any[]>([]);
  const [logLGSPResults, setLGSPLogResults] = useState<any[]>([]);
  const [snackbarVisible, setSnackbarVisible] = useState<boolean>(false);
  const [statusInfo, setStatusInfo] = useState<string[]>([]);
  interface SyncStatus {
    status: number;
    msg: string;
    requestBody: any;
  }
  
  interface RecordStatus {
    code: string;
    syncStatus: SyncStatus[];
  }

  const TOKEN_EXPIRY_TIME = 360 * 1000; // 360 seconds in milliseconds
  

  const handleChangeText = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setIsError(false);
    setInputText(event.target.value);
    setMessage("");
    setSnackbarVisible(false);
    setLogResults([]);
  };

  const handleChangeOption = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setOption(event.target.value);
    setIsError(false);
    setMessage("");
    setSnackbarVisible(false);
    setLogResults([]);
  };

  const callApiVBDLIS = async (modelInput: any, type: any) => {
    try {
      const token = await getTokenVBDLIS();

      let url = "";
      switch (type) {
        case "TiepNhan":
          url = 'https://ilis-lpm-api.vnpt.vn/api/DVCIGateV2/TiepNhanHoSo';
          break;
        case "CapNhatBoSung":
          url = 'https://ilis-lpm-api.vnpt.vn/api/DVCIGateV2/BoSungHoSo';
          break;
        case "PhanHoiSaiKetQua":
          url = 'https://ilis-lpm-api.vnpt.vn/api/DVCIGateV2/CapNhatTrangThaiHoSo';
          break;
      }

      const response = await axios.post(
        url,
        modelInput,
        {
          headers: {
            Authorization: `Bearer ${token.data}`,
            'Content-Type': 'application/json',
          },
        }
      );
      // Tạo một đối tượng phản hồi có cấu trúc tương tự như `Response`
      return {
        ok: true,
        status: response.status,
        statusText: response.statusText,
        json: async () => response.data,
      };
    } catch (error: any) {
      // Trả về một đối tượng phản hồi lỗi có cấu trúc tương tự như `Response`
      return {
        ok: false,
        status: error.response?.status || 500,
        statusText: error.response?.statusText || 'Internal Server Error',
        json: async () => error.response?.data || { message: 'Unknown Error' },
      };
    }
  };

  const getTokenVBDLIS = async () => {
    try {
      const response = await axios.post('https://ilis-sso.vnpt.vn/connect/token', {
        UserName: 'integrate.cmu',
        Password: 'sCdjF7dUjd@P8b',
      });

      const token = response.data; // Giả sử API trả về token ở đây

      if (token) {
        console.log('Token:', token);
        return token;
      } else {
        console.error('Token not found');
        return null;
      }
    } catch (error) {
      console.error('Error getting token:', error);
      return null;
    }
  };

  const fetchToken = async (): Promise<string> => {
    const accessToken = localStorage.getItem("access_token");
    if (!accessToken) {
      console.error("Không tìm thấy accessToken trong localStorage");
      return "";
    }
    return accessToken;
  };

  const fetchTokenLGSP = async () => {
    const clientId = 'rGhJF8Kcvfm03aa9a7Ow9Fz4JSMa';       // Replace with your actual client ID
    const clientSecret = 'CywywOKjWotLDVoffxojYopaXrka'; // Replace with your actual client secret
  
    // Use btoa to encode the client ID and client secret
    const encodedCredentials = btoa(`${clientId}:${clientSecret}`);
  
    const data = qs.stringify({
      'grant_type': 'client_credentials'
    });
  
    try {
      const response = await fetch('https://lgsp.cqdtcamau.vn/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${encodedCredentials}` // Use encoded credentials here
        },
        body: data
      });
  
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
  
      const responseData = await response.json();
      return responseData.access_token;
    } catch (error) {
      console.error("Error fetching access token:", error);
      return null;  // Return null in case of an error
    }
  };

  function extractDocIdFromUrl(url: any) {
    console.log(url);
    if (typeof url !== "string") {
      return url;
    }

    const regex = /DocId=([a-f0-9-]+)/;
    const match = url.match(regex);
    return match ? match[1] : url; // Giữ nguyên url nếu không có DocId
  }

  const handleCheckTrangThaiDVCLT = async () => {
    const token = await fetchToken();
    const records = inputText.split(",").map((record) => record.trim());
    setLoading(true);
    setIsError(false);
    setMessage("");
    setStatusInfo([]); 
  
    try {
      const response = await fetch(
        `${API_BASE_URL}/integration/api/lienthongDVCLT/getLog?nationCode=${records.join(",")}&page=0&size=100`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
  
      if (!response.ok) throw new Error("Lỗi khi gọi API");
  
      const data = await response.json();
      const statusInfo: any[] = [];
  
      for (const record of records) {
        const recordLogs = data.content.filter(
          (log: any) => log.dossierCode === record
        );
  
        const rs = {
          code: record,
          syncStatus: [] as { status: number; msg: string , requestBody: any}[],
        };
  
        const listStatus = [1, 3, 7, 4, 5];
  
        for (const status of listStatus) {
          const matchingLog = recordLogs.find(
            (log: any) =>
              log.requestBody?.trangThai === status
          );
  
          rs.syncStatus.push({
            status,
            msg: matchingLog
              ? matchingLog.responseBody?.status !== 200
                ? "Không thể kết nối đến DVCLT"
                : "Thành công"
              : "Không có thông tin",
            requestBody: matchingLog?.requestBody
          });
        }
  
        statusInfo.push(JSON.stringify(rs));
      }
  
      setMessage("Lấy dữ liệu thành công!");
      setStatusInfo(statusInfo);
    } catch (error: any) {
      setMessage(`API call error: ${error.message}`);
      setIsError(true);
    } finally {
      setLoading(false);
    }
  };
  
  
  

  const handleRetry = async (status: number, recordCode: string, requestBody: any) => {
    try {
      const token = await fetchToken();
      if(status === 1 || status === 3 || status === 7) {
        
        let apiUrl = `${API_BASE_URL}/integration/api/lienthongDVCLT/capNhatTrangThaiHoSoDVCLTHoTich`;
            const response1 = await fetch(apiUrl, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
              body: JSON.stringify(requestBody), // Sử dụng toàn bộ updatedModelInput
            });
            if (response1.ok) {
              const data = await response1.json();
              setMessage(`Đã cập nhật trạng thái ${status} cho hồ sơ ${recordCode}`);
            } else {
              setMessage(`Failed to update ${recordCode}`);
              setIsError(true);
            }
          

      } else {
        const syncUrl = `${API_BASE_URL}/pa/judicial-civil-status/--sync-dossiers?code=${recordCode}`;
        const syncResponse = await fetch(syncUrl, {
                  method: "POST",
                  headers: {
                    Authorization: `Bearer ${token}`,
                  },
                });
        if (syncResponse.ok) {
          setMessage(`Đã cap nhật trạng thái ${status} cho hồ sơ ${recordCode}`);
        }
        else {
          setMessage(`Failed to update ${recordCode}: ${syncResponse.status} ${syncResponse.statusText}`);
          setIsError(true);
        }

      }
    } catch (error) {
      console.error("Error in handleRetry:", error);
    }
  };
  

  const handleLogCheck = async () => {
    const records = inputText.split(",").map((record) => record.trim());
    const token = await fetchToken();
    const logResults: any[] = [];  // Cập nhật định nghĩa của logResults
    setLoading(true);
    setIsError(false);
    setMessage("");
    setLogResults([]);  // Reset logResults trước khi lấy dữ liệu mới

    try {
      for (const record of records) {
        try {
          const response = await fetch(
            `${API_BASE_URL}/integration/api/lienthongDVCLT/getLog?code=${record}`,
            {
              method: "GET",
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (response.ok) {
            const data = await response.json();
            logResults.push(...data.content);
          }
        } catch (error) {
          console.error(`Failed to fetch log for record ${record}:`, error);
        }
      }

      setMessage("Lấy dữ liệu thành công !");
      setLogResults(logResults);  // Cập nhật logResults với dữ liệu mới
    } catch (error: any) {
      setMessage(`API call error: ${error.message}`);
      setIsError(true);
    } finally {
      setLoading(false);
    }
  };


  const handleLogCheckLGSP = async () => {
    const tokenIgate = await fetchToken();
    const records = inputText.split(",").map((record) => record.trim());
    //create alert input input token after save LocalStorage with key access_token_lgsp
    const token = await fetchTokenLGSP();
     // Retrieve or prompt for tokenLGSP
    // Retrieve token and check expiry
    // let tokenLGSP = localStorage.getItem("access_token_lgsp");
    // const tokenTimestamp = localStorage.getItem("token_timestamp");
  
    // if (!tokenLGSP || (tokenTimestamp && Date.now() - Number(tokenTimestamp) > TOKEN_EXPIRY_TIME)) {
    //   // Token is missing or expired, prompt user to renew
    //   tokenLGSP = prompt("Your LGSP token has expired or is missing. Please enter a new token:");
    //   if (tokenLGSP) {
    //     // Save new token and timestamp
    //     localStorage.setItem("access_token_lgsp", tokenLGSP);
    //     localStorage.setItem("token_timestamp", Date.now().toString());
    //     alert("Token has been successfully saved to localStorage.");
    //   } else {
    //     alert("No token provided. Cannot proceed.");
    //     return; // Exit if no token is entered
    //   }
    // }
    const logLGSPResults: any[] = [];  // Cập nhật định nghĩa của logResults
    setLoading(true);
    setIsError(false);
    setMessage("");
    setLGSPLogResults([]);  // Reset logResults trước khi lấy dữ liệu mới

    try {
      for (const record of records) {
        //get dossier by code 
        const dossierUrl = `${API_BASE_URL}/pa/dossier/${record}/--by-code`;
        const dossierResponse = await fetch(dossierUrl, {
          method: "GET",
          headers: {
            Authorization: `Bearer ${tokenIgate}`,
          },
        });

        if(dossierResponse.ok) {
          const dossierData = await dossierResponse.json();
          const procedureDVCLT = dossierData.procedure.code;
          let moduleLT = '';
          if(procedureDVCLT === '1.000656.000.00.00.H12') {
             moduleLT = 'LTKT';
          }else{
            moduleLT = 'LTKS';
          }

          let data = JSON.stringify({
            "maDinhDanhHoSo": [
              record
            ],
            "module": moduleLT,
            "maTinh": 96
          });

          const response = await axios.post('https://lgsp.cqdtcamau.vn/apiLienThongKSKT/1.0/layKetQua', data, {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            }
          });
          if(response.status === 200) {
            console.log(response.data.value[0]);
            logLGSPResults.push(response.data.value[0]);
          }
          
        }
      }

      setMessage("Lấy dữ liệu thành công !");
      setLGSPLogResults(logLGSPResults);  // Cập nhật logResults với dữ liệu mới
    } catch (error: any) {
      setMessage(`API call error: ${error.message}`);
      setIsError(true);
    } finally {
      setLoading(false);
    }
  };

  const handleResend = async (index: number) => {
    // Xác nhận trước khi gửi lại
    const confirmed = window.confirm(
      "Bạn đã kiểm tra lại kiểu timestamp hay chưa? Xác nhận gửi lại?"
    );

    if (!confirmed) return;

    const token = await fetchToken();
    const log = logResults[index];
    const modelInput = JSON.parse(log.modelInput);
    const pathApi = log.pathApi;
    const method = log.method || "POST"; // Default to POST if method is not provided

    // Kiểm tra và chuyển đổi các field timestamp sang UTC
    if (modelInput.NgayHenTraMoi) {
      modelInput.NgayHenTraMoi = convertTimestampToUTC(modelInput.NgayHenTraMoi);
    }

    if (modelInput.NgayChoBoSung) {
      modelInput.NgayChoBoSung = convertTimestampToUTC(modelInput.NgayChoBoSung);
    }

    if (modelInput.NgayTiepNhan) {
      modelInput.NgayTiepNhan = convertTimestampToUTC(modelInput.NgayTiepNhan);
    }

    if (modelInput.NgayHenTra) {
      modelInput.NgayHenTra = convertTimestampToUTC(modelInput.NgayHenTra);
    }

    if (modelInput.ThongTinNguoiNopDon?.NgaySinh) {
      modelInput.ThongTinNguoiNopDon.NgaySinh = convertTimestampToUTC(modelInput.ThongTinNguoiNopDon.NgaySinh);
    }

    // Xử lý DanhSachGiayToDinhKem
    if (Array.isArray(modelInput.DanhSachGiayToDinhKem)) {
      for (let i = 0; i < modelInput.DanhSachGiayToDinhKem.length; i++) {
        const giayTo = modelInput.DanhSachGiayToDinhKem[i];

        // Loại bỏ "null," khỏi TapTin.data và gọi API để lấy dữ liệu base64 và filename mới
        if (giayTo.TapTin?.data) {
          try {
            const ids = giayTo.TapTin.data.replace("null,", "");
            const response = await fetch(
              `https://ketnoi.dichvucongcamau.gov.vn/fi/file/--base64?ids=${ids}`,
              {
                method: "GET",
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              }
            );

            if (response.ok) {
              const data = await response.json();
              giayTo.TapTin.data = data[0].base64;
              giayTo.TapTin.name = data[0].filename;
            }
          } catch (error: any) {
            console.error('Error fetching file data:', error);
          }
        }

      }
    }

    setLoading(true);
    setIsError(false);
    setMessage("");

    try {
      let response;

      // Kiểm tra nếu `pathApi` có chứa chuỗi `--receiving-dossier`
      if (pathApi.includes("--receiving-dossier")) {
        response = await callApiVBDLIS(modelInput, "TiepNhan"); // Gọi API tiếp nhận và trả về response
      } else if (pathApi.includes("--update-additional-request")) {
        response = await callApiVBDLIS(modelInput, "CapNhatBoSung");
      } else if (pathApi.includes("--feedback-profile-result-dossier")) {
        response = await callApiVBDLIS(modelInput, "PhanHoiSaiKetQua");
      }
      else {
        // Nếu không có `--receiving-dossier`, thực hiện API call với `fetch`
        response = await fetch(pathApi, {
          method,
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(modelInput),
        });
      }

      // Xử lý phản hồi từ cả `fetch` và `callTiepNhanApi`
      if (response.ok) {

        if (pathApi.includes("--receiving-dossier")) {
          const responseJson = await response.json();

          if (responseJson.data === 2 || responseJson.data === 1)
            nextTask(modelInput.SoBienNhan, 2, '', 2, token);
        }

        const data = await response.json(); // hoặc `response.data` nếu là axios
        setMessage(`Thành công: ${JSON.stringify(data)}`);
        setIsError(false);
      } else {
        // Xử lý trường hợp API call thất bại
        setMessage(`Thất bại: ${response.status} - ${response.statusText}`);
        setIsError(true);
      }
    } catch (error: any) {
      // Xử lý lỗi xảy ra khi thực hiện API call
      setMessage(`Lỗi xảy ra: ${error.message}`);
      setIsError(true);
    } finally {
      // Hiển thị snackbar và tắt trạng thái loading
      setSnackbarVisible(true);
      setLoading(false);
    }

  };

  // Hàm chuyển đổi timestamp sang định dạng ngày giờ UTC
  const convertTimestampToUTC = (timestamp: any) => {
    if (!timestamp) return null;

    const date = new Date(timestamp);
    const formattedDate = new Date(
      Date.UTC(
        date.getUTCFullYear(),
        date.getUTCMonth(),
        date.getUTCDate(),
        date.getUTCHours(),
        date.getUTCMinutes(),
        date.getUTCSeconds(),
        date.getUTCMilliseconds()
      )
    );

    // Chuyển đổi sang định dạng ISO 8601, bỏ qua một số lẻ không cần thiết
    const isoString = formattedDate.toISOString().slice(0, -1);

    return isoString;
  };

  const handleLogResultChange = (index: number, field: string, value: any) => {
    const updatedLogResults = [...logResults];
    updatedLogResults[index] = {
      ...updatedLogResults[index],
      [field]: value,
    };
    setLogResults(updatedLogResults);
  };

  const nextTask = async (code: any, level: any, condition: any, statusVBDLIS: any, token: any) => {
    const apiUrl = `${API_BASE_URL}/pa/ilis/--next-task?code=${code}&level=${level}&condition=${condition}&status-ilis=${statusVBDLIS}`;
    const response = await fetch(apiUrl, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  };

  const handleClearLog = () => {
    setLogResults([]);
    setLGSPLogResults([]);
    setStatusInfo([]);

    setMessage("");
  }

  const handleSubmit = async () => {
    if (
      inputText.trim() === ""
    ) {
      setMessage("Vui lòng nhập danh sách hồ sơ.");
      setIsError(true);
      return;
    }

    setLoading(true);
    setIsError(false);
    setMessage("");
    setLogResults([]);

    const records = inputText.split(",").map((record) => record.trim());

    switch (option) {
        case "Đồng bộ trả kết quả":
          try {
            const token = await fetchToken();
            const results: string[] = [];
            setLGSPLogResults([]);
            setLogResults([]);
  
            for (const record of records) {

              //check mã hồ sơ là có chữ H hay không
              if (record.includes("H")) {
                const syncUrl = `${API_BASE_URL}/pa/judicial-civil-status/--sync-dossiers?code=${record}`;
                const syncResponse = await fetch(syncUrl, {
                  method: "POST",
                  headers: {
                    Authorization: `Bearer ${token}`,
                  },
                });
    
                if (syncResponse.ok) {
                  results.push(
                    `Đồng bộ thành công hồ sơ ${record}`
                  );
                } else {
                  results.push(`Lỗi đồng bộ hồ sơ ${record}.`);
                  setIsError(true);
                }
              }
              else {
                const dossierUrl = `${API_BASE_URL}/pa/dossier/${record}/--by-nation-code`;
                const dossierResponse = await fetch(dossierUrl, {
                  method: "GET",
                  headers: {
                    Authorization: `Bearer ${token}`,
                  },
                });
                if (dossierResponse.ok) {
                  const dossierData = await dossierResponse.json();
                  const dossierCode = dossierData.code;
                  console.log(dossierCode);
                  const syncUrl = `${API_BASE_URL}/pa/judicial-civil-status/--sync-dossiers?code=${dossierCode}`;
                  const syncResponse = await fetch(syncUrl, {
                    method: "POST",
                    headers: {
                      Authorization: `Bearer ${token}`,
                    },
                  });
      
                  if (syncResponse.ok) {
                    results.push(
                      `Đồng bộ thành công hồ sơ ${dossierCode}`
                    );
                  } else {
                    results.push(`Lỗi đồng bộ hồ sơ ${dossierCode}.`);
                    setIsError(true);
                  }

                } else {
                  results.push(`Lỗi đồng bộ hồ sơ ${record}.`);
                  setIsError(true);
                }
  
              }
             
            }
            setMessage(results.join("\n"));
          } catch (error: any) {
            setMessage(`API call error: ${error.message}`);
            setIsError(true);
          } finally {
            setLoading(false);
          }
          break;


      case "Tiếp nhận qua PM Hộ tịch tư pháp":
        try {
          const token = await fetchToken();
          const results: string[] = [];
          setLGSPLogResults([]);
          setLogResults([]);

          for (const record of records) {
            const dossierUrl = `${API_BASE_URL}/pa/judicial-civil-status/--send?code=${record}`;
            const dossierResponse = await fetch(dossierUrl, {
              method: "POST",
              headers: {
                Authorization: `Bearer ${token}`,
              },
            });
            const data = await dossierResponse.json();

            if (data.status != 500) {
              results.push(
                `Đông bộ thành công hồ sơ ${record}`
              );
            } else {
              results.push(`Lỗi đồng bộ hồ sơ ${record}.`);
              setIsError(true);
              setMessage(data.error + "\n" + data.errors);
            }
          }
          setMessage(results.join("\n"));
        } catch (error: any) {
          setMessage(`API call error: ${error.message}`);
          setIsError(true);
        } finally {
          setLoading(false);
        }
        break;

      case "Đồng bộ trạng thái DVCLT":
        try {
          const token = await fetchToken();
          const results: string[] = [];

          // Kiểm tra xem records và logResults có phải là mảng và không rỗng
          if (!Array.isArray(records) || records.length === 0) {
            throw new Error("Records is not an array or is empty");
          }

          if (!Array.isArray(logResults) || logResults.length === 0) {
            throw new Error("logResults is not an array or is empty");
          }

          for (const record of records) {
            const log = logResults.find((item) => item.requestBody.maHoSo === record);

            if (!log) {
              results.push(`Log not found for record ${record}`);
              setIsError(true);
              continue;
            }

            const updatedModelInput = { ...JSON.parse(log.requestBody) };

            let apiUrl = `${API_BASE_URL}/integration/api/lienthongDVCLT/capNhatTrangThaiHoSoDVCLTHoTich`;

            const response = await fetch(apiUrl, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
              body: JSON.stringify(updatedModelInput), // Sử dụng toàn bộ updatedModelInput
            });

            if (response.ok) {
              const data = await response.json();
              results.push(
                `Updated ${record} successfully: ${JSON.stringify(data)}`
              );
            } else {
              results.push(
                `Failed to update ${record}: ${response.status} ${response.statusText}`
              );
              setIsError(true);
            }
          }

          setMessage(results.join("\n"));
        } catch (error: any) {
          setMessage(`API call error: ${error.message}`);
          setIsError(true);
        } finally {
          setLoading(false);
        }
        break;
      
      case "Đồng bộ iGate":
        try {
          const token = await fetchToken();
          const results: string[] = [];
          setLGSPLogResults([]);
          setLogResults([]);

          for (const record of records) {
            //Get log by nation code
            const dossierUrl = `${API_BASE_URL}/integration/api/lienthongDVCLT/getLog?nationCode=${record}`;
            const dossierResponse = await fetch(dossierUrl, {
              method: "GET",
              headers: {
                Authorization: `Bearer ${token}`,
              },
            });
            
            const dossierData = await dossierResponse.json();
            let id = "";
            let securityKey = "";
            //get content of log
            for (let i = 0; i < dossierData.content.length; i++) {
              if(dossierData.content[i].api === 'POST /api/lienthongDVCLT/receiveRecord'){
                id = dossierData.content[i].id;
                securityKey = dossierData.content[i].securityKey;
              }
            }

            const headers = new Headers({
              'Authorization': `Bearer ${token}`,
              'securityKey': securityKey,
              'kafkaEnable': 'true',
              'Content-Type': 'application/json'
            });

            const syncUrl = `${API_BASE_URL}/integration/api/lienthongDVCLT/${id}/--resend`;
            const syncResponse = await fetch(syncUrl, {
              method: "POST",
              headers: headers,
            });

            const responseText = await syncResponse.text();
            if (responseText === "true") {
              results.push(
                `Đồng bộ thành công hồ sơ ${record}`
              );
            } else {
              results.push(`Lỗi đồng bộ hồ sơ ${record}.`);
              setIsError(true);
            }
          }
          setMessage(results.join("\n"));
        } catch (error: any) {
          setMessage(`API call error: ${error.message}`);
          setIsError(true);
        } finally {
          setLoading(false);
        }
        break;
      case "Đồng bộ LGSP":
        try {
          const token = await fetchToken();
          const results: string[] = [];
          setLGSPLogResults([]);
          setLogResults([]);

          for (const record of records) {
            //Get log by nation code
            const dossierUrl = `${API_BASE_URL}/integration/api/lienthongDVCLT/getLog?nationCode=${record}`;
            const dossierResponse = await fetch(dossierUrl, {
              method: "GET",
              headers: {
                Authorization: `Bearer ${token}`,
              },
            });
            
            const dossierData = await dossierResponse.json();
            let id = "";
            let securityKey = "";
            //get content of log
            for (let i = 0; i < dossierData.content.length; i++) {
              if(dossierData.content[i].api === 'POST /api/lienthongDVCLT/receiveRecord'){
                id = dossierData.content[i].id;
                securityKey = dossierData.content[i].securityKey;
              }
            }

            const headers = new Headers({
              'Authorization': `Bearer ${token}`,
              'securityKey': securityKey,
              'Content-Type': 'application/json'
            });

            const syncUrl = `https://lgsp.cqdtcamau.vn/apiLienThongKSKT/1.0/nhanHoSoDKHT`;
            const syncResponse = await fetch(syncUrl, {
              method: "POST",
              headers: headers,
            });

            const responseText = await syncResponse.text();
            if (responseText === "true") {
              results.push(
                `Đồng bộ thành công hồ sơ ${record}`
              );
            } else {
              results.push(`Lỗi đồng bộ hồ sơ ${record}.`);
              setIsError(true);
            }
          }
          setMessage(results.join("\n"));
        } catch (error: any) {
          setMessage(`API call error: ${error.message}`);
          setIsError(true);
        } finally {
          setLoading(false);
        }
        break;

      default:
        setMessage(`You selected an unsupported option: ${option}`);
        setIsError(true);
        setLoading(false);
        break;
    }
  };

  return (
    <div className="form-cotainer">
      {option !== "a" && (
        <TextArea value={inputText} onChange={handleChangeText} />
      )}
      <SelectOption value={option} onChange={handleChangeOption} />
      <div className="processing-content-buttons">
        {option === "Đồng bộ trạng thái DVCLT" && (
          <button
            className="log-check-button"
            onClick={handleLogCheck}
            disabled={loading || !inputText.trim()}
          >
            <FaClipboardList />
            {loading ? "Đang chạy..." : "Kiểm tra log"}
          </button>
          
          
        )}

        {option === "Đồng bộ trạng thái DVCLT" && (
          <button
            className="red-button"
            onClick={handleLogCheckLGSP}
            disabled={loading || !inputText.trim()}
          >
            <FaClipboardList />
            {loading ? "Đang chạy..." : "Kiểm tra trạng thái PM Hộ tịch"}
          </button>
          
        )}

        {option === "Đồng bộ trạng thái DVCLT" && (
          <button
            className="submit-button"
            onClick={handleCheckTrangThaiDVCLT}
            disabled={loading || !inputText.trim()}
          >
            <FaClipboardList />
            {loading ? "Đang chạy..." : "Kiểm tra trạng thái đồng bộ DVCLT"}
          </button>
          
        )}

        {option !== "Đồng bộ trạng thái DVCLT" && (
          <button
            className="submit-button"
            onClick={handleSubmit}
            disabled={loading || !inputText.trim()}
          >
            <FaSync />
            {loading ? "Đang chạy..." : "Đồng bộ"}
          </button>
          
        )}
        <button
          className="orange-button"
          onClick={() => handleClearLog()}>
          <FaTrash /> Xóa log
          </button>

        
      </div>
      <MessageDisplay message={message} isError={isError} />
      {snackbarVisible && (
        <Snackbar
          message={message}
          isError={isError}
          onClose={() => setSnackbarVisible(false)}
        />
      )}
      {logResults.length > 0 && (
        <div className="log-results">
          {logResults.map((result, index) => (
            <div key={index} className="log-item">
              {index === 0 || result.code !== logResults[index - 1].code ? (
                <div className="dossier-code">
                  Mã hồ sơ: {result.dossierCode}
                </div>
              ) : null}
              <label>Request Body:</label>
              <textarea
                value={JSON.stringify(result.requestBody, null, 2)}
                onChange={(e) =>
                  handleLogResultChange(index, "requestBody", e.target.value)
                }
              />
              <label>response Body:</label>
              <textarea
                value={JSON.stringify(result.responseBody, null, 2)}
                onChange={(e) =>
                  handleLogResultChange(index, "responseBody", e.target.value)
                }
              />
              <label>Result:</label>
              <input
                type="text"
                value={result.result}
                onChange={(e) =>
                  handleLogResultChange(index, "result", e.target.value)
                }
              />
              <label>Create Date:</label>
              <input
                type="text"
                value={result.callTime}
                onChange={(e) =>
                  handleLogResultChange(index, "callTime", e.target.value)
                }
              />
              <label>Path Api:</label>
              <input
                type="text"
                value={result.pathApi}
                onChange={(e) =>
                  handleLogResultChange(index, "pathApi", e.target.value)
                }
              />
              <label>Method:</label>
              <select
                value={result.method || ""}
                onChange={(e) =>
                  handleLogResultChange(index, "method", e.target.value)
                }
              >
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="GET">GET</option>
                <option value="DELETE">DELETE</option>
              </select>
              <div>

              </div>
              <div className="processing-content-buttons">
                <button onClick={() => handleResend(index)}><FaReply />{loading ? "Đang chạy..." : "Gọi lại"}</button>
              </div>
            </div>
          ))}
        </div>
      )}
    {logLGSPResults.length > 0 && (
        <div className="log-results">
          {logLGSPResults.map((result, index) => (
            <div key={index} className="log-item">
              {index === 0 || result.maHoSoMCDT !== logLGSPResults[index - 1].maHoSoMCDT ? (
                <div className="dossier-code">
                  Mã hồ sơ: {result.maHoSoMCDT}
                </div>
              ) : null}
              <label>Trạng thái PM Hộ tịch tư pháp:</label>
              <input
                type="text"
                value={
                  result.trangThai === 2
                    ? "Hồ sơ cần bổ sung thông tin"
                    : result.trangThai === 3
                    ? "Hồ sơ đủ điều kiện giải quyết"
                    : result.trangThai === 4
                    ? "Đã hoàn thành đăng ký"
                    : result.trangThai === 6
                    ? "Từ chối tiếp nhận"
                    : ""
                }/>
               <textarea
                value={JSON.stringify(result, null, 2)}
               
              />
            </div>
            
          ))}
        </div>
      )}

<div className="status-log" style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      {statusInfo.map((record, index) => {
        const parsedRecord: RecordStatus = JSON.parse(record);
        
        return (
          <div 
            key={index}
            style={{
              backgroundColor: '#fff',
              borderRadius: '8px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              padding: '16px',
              marginBottom: '16px'
            }}
          >
            <div style={{ 
              fontSize: '18px', 
              fontWeight: 'bold',
              marginBottom: '12px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <span style={{ 
                display: 'inline-block',
                width: '20px',
                height: '20px',
                borderRadius: '50%',
                backgroundColor: '#e2e8f0'
              }}/>
              Mã hồ sơ: {parsedRecord.code}
            </div>

            {parsedRecord.syncStatus.map((sync: SyncStatus, statusIndex: number) => {
              const hasError = sync.msg.toLowerCase().includes('không');
              
              return (
                <div 
                  key={statusIndex}
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 0',
                    borderTop: statusIndex === 0 ? 'none' : '1px solid #e2e8f0'
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{
                      display: 'inline-block',
                      width: '12px',
                      height: '12px',
                      borderRadius: '50%',
                      backgroundColor: hasError ? '#ef4444' : '#22c55e'
                    }}/>
                    <span>
                      <strong>Trạng thái {sync.status}:</strong> {sync.msg}
                    </span>
                  </div>

                  {hasError && (
                    <button
                      onClick={() => handleRetry(sync.status, parsedRecord.code, sync.requestBody)}
                      style={{
                        backgroundColor: '#ef4444',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        padding: '6px 12px',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '6px',
                        fontSize: '14px',
                        transition: 'background-color 0.2s'
                      }}
                    
                    >
                      <span style={{
                        display: 'inline-block',
                        width: '14px',
                        height: '14px',
                        border: '2px solid currentColor',
                        borderRadius: '50%',
                        borderRightColor: 'transparent',
                      }}/>
                      Gọi lại
                    </button>
                  )}
                </div>
              );
            })}
          </div>
        );
      })}
    </div>




    </div>
  );
};

export default FormDVCLTPage;